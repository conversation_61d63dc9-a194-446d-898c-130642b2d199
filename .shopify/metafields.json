{"article": [{"key": "min_reading", "namespace": "custom", "name": "Min Reading", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "blog": [], "collection": [], "company": [], "company_location": [], "location": [], "market": [], "order": [{"key": "crm_id", "namespace": "custom", "name": "crm_id", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "page": [], "product": [{"key": "queries", "namespace": "shopify--discovery--product_search_boost", "name": "Search product boosts", "description": "List of search queries for which a product gets higher rank in search results", "type": {"name": "list.single_line_text_field", "category": "TEXT"}}, {"key": "related_products", "namespace": "shopify--discovery--product_recommendation", "name": "Related products", "description": "List of related products", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "related_products_display", "namespace": "shopify--discovery--product_recommendation", "name": "Related products settings", "description": "Determines how related products are displayed along with algorithmic product recommendations", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "complementary_products", "namespace": "shopify--discovery--product_recommendation", "name": "Complementary products", "description": "List of complementary products", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "custom_badge", "namespace": "custom", "name": "Custom Badge", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "related_products", "namespace": "product_info", "name": "Bundle Products", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "crm_id", "namespace": "custom", "name": "crm_id", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_label", "namespace": "custom", "name": "תווית המוצר", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "custom_product", "namespace": "mm-google-shopping", "name": "Google: Custom Product", "description": "Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.", "type": {"name": "boolean", "category": "TRUE_FALSE"}}, {"key": "detail_1", "namespace": "custom", "name": "Detail 1 ", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "detail_2", "namespace": "custom", "name": "Detail 2", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "detail_3", "namespace": "custom", "name": "Detail 3", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "detail_4", "namespace": "custom", "name": "Detail 4", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "detail_5", "namespace": "custom", "name": "Detail 5", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "popup_image", "namespace": "custom", "name": "Popup Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "lemi_matim", "namespace": "custom", "name": "למי זה מתאים", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "How_use", "namespace": "custom", "name": "איך להשתמש במוצר", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "<PERSON><PERSON><PERSON>", "namespace": "custom", "name": "איך לאחסן את המוצר", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "pro_banner_image_desktop", "namespace": "custom", "name": "בא<PERSON>ר רשימת רכיבים דסקטופ", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "pro_banner_image_mobile", "namespace": "custom", "name": "בא<PERSON>ר רשימת רכיבים מובייל", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "slider_with_content_heading", "namespace": "custom", "name": "Slide<PERSON> with Content heading", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_description", "namespace": "custom", "name": "Slider with Content description", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "slider_with_content_title_1", "namespace": "custom", "name": "Slider with Content title 1", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_subtitle_1", "namespace": "custom", "name": "Slider with Content subtitle 1", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_image_1", "namespace": "custom", "name": "Slider with Content Image 1", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "slider_with_content_title_2", "namespace": "custom", "name": "Slider with Content title 2", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_subtitle_2", "namespace": "custom", "name": "Slider with Content subtitle 2 ", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_image_2", "namespace": "custom", "name": "Slider with Content Image 2", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "slider_with_content_title_3", "namespace": "custom", "name": "Slider with Content title 3", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_subtitle_3", "namespace": "custom", "name": "Slider with Content subtitle 3", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_image_3", "namespace": "custom", "name": "Slider with Content Image 3", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "slider_with_content_title_4", "namespace": "custom", "name": "Slider with Content title 4", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_subtitle_4", "namespace": "custom", "name": "Slider with Content subtitle 4", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_image_4", "namespace": "custom", "name": "Slider with Content Image 4", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "slider_with_content_title_5", "namespace": "custom", "name": "Slider with Content title 5", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_subtitle_5", "namespace": "custom", "name": "Slider with Content subtitle 5", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "slider_with_content_image_5", "namespace": "custom", "name": "Slider with Content Image 5", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "nutritional_values", "namespace": "custom", "name": "nutritional-values", "description": "ערכים תזונתיים", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "valuesDescription", "namespace": "nutritional", "name": "רכיבים", "description": "custom.Nutritional_Values", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "a<PERSON>him", "namespace": "custom", "name": "רכיבים", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "more", "namespace": "custom", "name": "עוד דברים חשובים לדעת", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "klali", "namespace": "custom", "name": "מה המינון המומלץ?", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "ingridiants_list", "namespace": "custom", "name": "ingridiants-list", "description": "", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "meta_image", "namespace": "custom", "name": "סליידר רשימת רכיבים", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}], "variant": [{"key": "custom_label_4", "namespace": "mm-google-shopping", "name": "Google: Custom Label 4", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_3", "namespace": "mm-google-shopping", "name": "Google: Custom Label 3", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_2", "namespace": "mm-google-shopping", "name": "Google: Custom Label 2", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_1", "namespace": "mm-google-shopping", "name": "Google: Custom Label 1", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_0", "namespace": "mm-google-shopping", "name": "Google: Custom Label 0", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_system", "namespace": "mm-google-shopping", "name": "Google: Size System", "description": "The country of the size system used by your product.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_type", "namespace": "mm-google-shopping", "name": "Google: Size Type", "description": "Your apparel product’s cut.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "mpn", "namespace": "mm-google-shopping", "name": "Google: MPN", "description": "Your product’s Manufacturer Part Number (MPN).", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "gender", "namespace": "mm-google-shopping", "name": "Google: Gender", "description": "The gender for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "condition", "namespace": "mm-google-shopping", "name": "Google: Condition", "description": "The condition of your product at time of sale.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "age_group", "namespace": "mm-google-shopping", "name": "Google: Age Group", "description": "The demographic for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "shop": []}